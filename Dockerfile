# Stage 1: Build the Flutter web application
FROM ubuntu:latest AS build

# Install necessary dependencies
RUN apt-get update && \
    apt-get install -y git curl unzip libglu1-mesa && \
    rm -rf /var/lib/apt/lists/*

# Download and install Flutter SDK
ENV FLUTTER_VERSION 3.35.2
RUN git clone https://github.com/flutter/flutter.git /usr/local/flutter && \
    cd /usr/local/flutter && \
    git checkout tags/$FLUTTER_VERSION

ENV PATH="/usr/local/flutter/bin:/usr/local/flutter/bin/cache/dart-sdk/bin:${PATH}"

# Enable web support
RUN flutter channel stable && \
    flutter config --enable-web

WORKDIR /app

# Copy the Flutter project files
COPY . .

# Get Flutter packages and build the web application
RUN flutter pub get && \
    flutter build web --release

# Stage 2: Serve the built application with Nginx
FROM nginx:alpine

# Copy the built Flutter web app from the build stage
COPY --from=build /app/build/web /usr/share/nginx/html
COPY .well-known /usr/share/nginx/html/.well-known
COPY default.conf /etc/nginx/conf.d 

# Expose port 80 for web traffic
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
