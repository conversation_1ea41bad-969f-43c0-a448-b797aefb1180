name: CI/CD Pipeline
on:
  push:
    branches: ["staging", "develop", "master"]

jobs:
  develop-workflow:
    if: github.ref == 'refs/heads/develop'
    uses: nft-passport-llc/ci-templates/.github/workflows/default-template-all.yaml@develop
    with:
      SERVICE_NAME: "toii-social-mobile-app"
      ARGOCD_APP_NAME: "dev-toii-social-mobile-app"
      DOCKERFILE_PATH: "./Dockerfile"
      SERVICE_MODULE: "."
      ARGOCD_SERVER: "argocd.toii.social"
      REPO_URL: "nft-passport-llc/toii-gitops"
      ENVIRONMENT: 'dev'
      PROJECT: 'toii'
    secrets:
      DOCKER_TOKEN: ${{ secrets.DOCKER_TOKEN }}
      ARGOCD_USERNAME: ${{ secrets.ARGOCD_USERNAME }}
      ARGOCD_PASSWORD: ${{ secrets.ARGOCD_PASSWORD }}
      REPO_TOKEN: ${{ secrets.REPO_TOKEN }}

  master-workflow:
    if: github.ref == 'refs/heads/master'
    uses: nft-passport-llc/ci-templates/.github/workflows/default-template-all.yaml@master
    with:
      SERVICE_NAME: "toii-social-mobile-app"
      ARGOCD_APP_NAME: "prod-toii-social-mobile-app"
      DOCKERFILE_PATH: "./Dockerfile"
      SERVICE_MODULE: "."
      ARGOCD_SERVER: "argocd.toii.social"
      REPO_URL: "nft-passport-llc/toii-gitops"
      ENVIRONMENT: 'prod'
      PROJECT: 'toii'
    secrets:
      DOCKER_TOKEN: ${{ secrets.DOCKER_TOKEN }}
      ARGOCD_USERNAME: ${{ secrets.ARGOCD_USERNAME }}
      ARGOCD_PASSWORD: ${{ secrets.ARGOCD_PASSWORD }}
      REPO_TOKEN: ${{ secrets.REPO_TOKEN }}
