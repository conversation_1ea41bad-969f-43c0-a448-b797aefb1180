import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:local_auth/local_auth.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

enum Biometric { faceid, touceId, none }

class AuthenticationService {
  final LocalAuthentication _auth = LocalAuthentication();

  final VoidCallback onLoginSuccess;
  final VoidCallback? onNotSupport;
  AuthenticationService({required this.onLoginSuccess, this.onNotSupport});

  static Future<Biometric> typeBioMetric() async {
    final LocalAuthentication auth = LocalAuthentication();

    List<BiometricType> availableBiometrics =
        await auth.getAvailableBiometrics();

    if (availableBiometrics.isEmpty) {
      return Biometric.none;
    } else if (Platform.isIOS) {
      if (availableBiometrics.contains(BiometricType.face)) {
        return Biometric.faceid;
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return Biometric.touceId;
      }
    } else {
      return Biometric.touceId;
    }
    return Biometric.none;
  }

  void authCheck(context) async {
    if (kIsWeb) {
      onLoginSuccess.call();
      return;
    }
    List<BiometricType> availableBiometrics =
        await _auth.getAvailableBiometrics();

    if (availableBiometrics.isEmpty) {
      final topContext = GetIt.instance<NavigationService>().navigatorContext;
      onNotSupport?.call();
      if (topContext.mounted) {
        topContext.showSnackbar(message: S.current.biometric_not_support);
      }
    } else if (Platform.isIOS) {
      if (availableBiometrics.contains(BiometricType.face)) {
        startBioMetricAuth(context, S.current.biometric_use_face_id);
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        startBioMetricAuth(context, S.current.biometric_use_touch_id);
      }
    } else {
      startBioMetricAuth(context, S.current.biometric_use_fingerprint);
    }
  }

  Future<bool> defaultShowBio() async {
    List<BiometricType> availableBiometrics =
        await _auth.getAvailableBiometrics();
    return availableBiometrics.isNotEmpty;
  }

  void startBioMetricAuth(context, String message) async {
    //  onLoginSuccess.call();
    try {
      bool didAuthenticate = await _auth.authenticate(localizedReason: message);
      if (didAuthenticate) {
        onLoginSuccess.call();
      } else {
        final topContext = GetIt.instance<NavigationService>().navigatorContext;
        if (topContext.mounted) {
          topContext.showSnackbar(message: "Login failed");
        }
      }
    } on PlatformException {
      final topContext = GetIt.instance<NavigationService>().navigatorContext;
      if (topContext.mounted) {
        topContext.showSnackbar(message: "Login failed");
      }

      if (kDebugMode) {
        print("Error!");
      }
    }
  }
}
