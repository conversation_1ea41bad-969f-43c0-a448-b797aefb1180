import 'dart:convert';
import 'dart:developer';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:toii_social/flavors/flavors.dart';
import 'package:toii_social/model/credentials/credentials.dart';
import 'package:toii_social/model/user/user_model.dart';

class KeychainService {
  late final FlutterSecureStorage _storage;
  String isEnableBio = "key_Enable_Bio";
  String keyEmail = "key_Email";
  String keyPassword = "key_Password";
  String keyAddress = "key_Address";
  String _address = "address";
  String _privateKey = "privateKey";
  KeychainService._privateConstructor() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  static final KeychainService instance = KeychainService._privateConstructor();

  init() {
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );
  }

  Future<void> clearData() async {
    var value = await _storage.read(key: "isClear1") ?? "";
    if (value != "1") {
      await _storage.deleteAll(
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
      await _storage.write(key: "isClear1", value: "1");
    }
  }

  Future<String> getPasswordUser() async {
    var password = await _storage.read(key: keyPassword) ?? "";
    return password;
  }

  Future<String> getUserName() async {
    var email = await _storage.read(key: keyEmail) ?? "";
    return email;
  }

  void setUserName(String email) async {
    await _storage.write(key: keyEmail, value: email);
  }

  void setPassword(String password) async {
    await _storage.write(key: keyPassword, value: password);
  }

  void setEnableBio(bool value) async {
    await _storage.write(key: isEnableBio, value: value ? "1" : "0");
  }

  Future<bool> isEnableBiometric() async {
    var isEnable = await _storage.read(key: isEnableBio) ?? "";
    return int.tryParse(isEnable) == 1 ? true : false;
  }

  void clearAll() async {
    await _storage.deleteAll();
  }

  Future<void> savePrivateKeyToiCloud({
    required Credentials credentials,
  }) async {
    final jsonData = await _storage.read(
      key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
        synchronizable: true, // ✅ Enables iCloud Keychain sync
      ),
    );

    if (jsonData == null) {
      final map = {credentials.address: credentials.privateKeyHex};
      final jsonData = jsonEncode(map);
      await _storage.write(
        key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
        value: jsonData,
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      final decoded = jsonDecode(jsonData);
      if (decoded[credentials.address] == null) {
        decoded[credentials.address] = credentials.privateKeyHex;
      } 
      //decoded[credentials.address] = credentials.privateKeyHex;
      final updatedJsonData = jsonEncode(decoded);
      await _storage.write(
        key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
        value: updatedJsonData,
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    }
  }

  void setProfile(UserModel profile) async {
    final jsonData = await _storage.read(
      key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_profile',
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
        synchronizable: true, // ✅ Enables iCloud Keychain sync
      ),
    );
    if (profile.address == null) return;

    if (jsonData == null) {
      final map = {profile.walletAddress!: profile.toJson()};
      final jsonData = jsonEncode(map);
      await _storage.write(
        key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_profile',
        value: jsonData,
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    } else {
      final decoded = jsonDecode(jsonData);
      decoded[profile.walletAddress] = profile.toJson();
      final updatedJsonData = jsonEncode(decoded);
      await _storage.write(
        key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_profile',
        value: updatedJsonData,
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
    }
  }

  Future<List<String>> getListWallet() async {
    final jsonData = await _storage.read(
      key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
        synchronizable: true, // ✅ Enables iCloud Keychain sync
      ),
    );

    final Map<String, dynamic> decoded = jsonDecode(jsonData ?? "");

    final keys = decoded.keys.toList();
    return keys;
  }

  Future<String?> readPrivateKeyFromiCloud(String address) async {
    try {
      final jsonData = await _storage.read(
        key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );

      if (jsonData == null) return null;
      final decoded = jsonDecode(jsonData);

      return decoded[address] as String?;
    } catch (e) {
      log("Error reading private key from iCloud: $e");
      return null;
    }
  }

  Future<Map<String, dynamic>> getProfile() async {
    final jsonData = await _storage.read(
      key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_profile',
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
        synchronizable: true, // ✅ Enables iCloud Keychain sync
      ),
    );

    final Map<String, dynamic> decoded = jsonDecode(jsonData ?? "");
    return decoded;
  }

  Future<bool> isFirstLaunch() async {
    var value = await _storage.read(key: "isClear1") ?? "";
    if (value != "1") {
      await _storage.deleteAll(
        iOptions: const IOSOptions(
          accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
          synchronizable: true, // ✅ Enables iCloud Keychain sync
        ),
      );
      await _storage.write(key: "isClear1", value: "1");
    }
    final jsonData = await _storage.read(
      key: '${F.appFlavor == Flavor.dev ? "" : "prod"}wallet_login1',
      iOptions: const IOSOptions(
        accessibility: KeychainAccessibility.unlocked, // or whenUnlocked
        synchronizable: true, // ✅ Enables iCloud Keychain sync
      ),
    );

    return (jsonData == null);
  }
}
