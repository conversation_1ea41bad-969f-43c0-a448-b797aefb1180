import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/model/follower/follower_model.dart';

part 'global_follow_management_state.dart';

/// Global cubit to manage follow relationships across the entire app
/// This ensures that follow status is synchronized across all components
class GlobalFollowManagementCubit extends Cubit<GlobalFollowManagementState> {
  GlobalFollowManagementCubit() : super(const GlobalFollowManagementState());

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  /// Initialize follow status for a user
  void initializeFollowStatus(String userId, bool isFollowing) {
    final updatedFollowMap = Map<String, bool>.from(state.followStatusMap);
    updatedFollowMap[userId] = isFollowing;
    
    emit(state.copyWith(followStatusMap: updatedFollowMap));
  }

  /// Get follow status for a specific user
  bool getFollowStatus(String userId) {
    return state.followStatusMap[userId] ?? false;
  }

  /// Toggle follow status for a user
  Future<void> toggleFollow(String userId, bool currentStatus) async {
    // Update UI immediately for better UX
    final updatedFollowMap = Map<String, bool>.from(state.followStatusMap);
    updatedFollowMap[userId] = !currentStatus;
    
    emit(state.copyWith(
      followStatusMap: updatedFollowMap,
      loadingUsers: {...state.loadingUsers, userId},
    ));

    try {
      if (!currentStatus) {
        // Follow user
        await _userRepository.followUser(FollowRequest(followedId: userId));
      } else {
        // Unfollow user
        await _userRepository.unfollowUser(userId);
      }
      
      // Remove from loading set on success
      final updatedLoadingUsers = Set<String>.from(state.loadingUsers);
      updatedLoadingUsers.remove(userId);
      
      emit(state.copyWith(
        loadingUsers: updatedLoadingUsers,
        errorMessage: null,
      ));
    } catch (e) {
      // Revert the UI change on error
      final revertedFollowMap = Map<String, bool>.from(state.followStatusMap);
      revertedFollowMap[userId] = currentStatus;
      
      final updatedLoadingUsers = Set<String>.from(state.loadingUsers);
      updatedLoadingUsers.remove(userId);
      
      emit(state.copyWith(
        followStatusMap: revertedFollowMap,
        loadingUsers: updatedLoadingUsers,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Update follow status for a user (used when status changes from other sources)
  void updateFollowStatus(String userId, bool isFollowing) {
    final updatedFollowMap = Map<String, bool>.from(state.followStatusMap);
    updatedFollowMap[userId] = isFollowing;
    
    emit(state.copyWith(followStatusMap: updatedFollowMap));
  }

  /// Check if a user is currently being processed (loading state)
  bool isUserLoading(String userId) {
    return state.loadingUsers.contains(userId);
  }

  /// Clear error message
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Batch update follow statuses (useful when loading feed data)
  void batchUpdateFollowStatuses(Map<String, bool> followStatuses) {
    final updatedFollowMap = Map<String, bool>.from(state.followStatusMap);
    updatedFollowMap.addAll(followStatuses);
    
    emit(state.copyWith(followStatusMap: updatedFollowMap));
  }
}
