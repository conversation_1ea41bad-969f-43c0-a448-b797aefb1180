part of 'global_follow_management_cubit.dart';

class GlobalFollowManagementState {
  /// Map of userId -> follow status
  final Map<String, bool> followStatusMap;
  
  /// Set of user IDs currently being processed (loading state)
  final Set<String> loadingUsers;
  
  /// Error message if any operation fails
  final String? errorMessage;

  const GlobalFollowManagementState({
    this.followStatusMap = const {},
    this.loadingUsers = const {},
    this.errorMessage,
  });

  GlobalFollowManagementState copyWith({
    Map<String, bool>? followStatusMap,
    Set<String>? loadingUsers,
    String? errorMessage,
  }) {
    return GlobalFollowManagementState(
      followStatusMap: followStatusMap ?? this.followStatusMap,
      loadingUsers: loadingUsers ?? this.loadingUsers,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is GlobalFollowManagementState &&
        other.followStatusMap.toString() == followStatusMap.toString() &&
        other.loadingUsers.toString() == loadingUsers.toString() &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return followStatusMap.hashCode ^
        loadingUsers.hashCode ^
        errorMessage.hashCode;
  }
}
