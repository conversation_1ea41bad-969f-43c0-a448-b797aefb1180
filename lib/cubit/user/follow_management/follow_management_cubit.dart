import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/user/global_follow_management/global_follow_management_cubit.dart';

part 'follow_management_state.dart';

class FollowManagementCubit extends Cubit<FollowManagementState> {
  FollowManagementCubit({
    required String userId,
    bool initialFollowingStatus = false,
  }) : super(
         FollowManagementState(
           userId: userId,
           isFollowing: initialFollowingStatus,
         ),
       ) {
    // Initialize the global follow status
    _globalFollowCubit.initializeFollowStatus(userId, initialFollowingStatus);

    // Listen to global follow state changes
    _globalFollowSubscription = _globalFollowCubit.stream.listen((globalState) {
      final currentFollowStatus = globalState.followStatusMap[userId] ?? false;
      final isLoading = globalState.loadingUsers.contains(userId);

      // Update local state based on global state
      if (state.isFollowing != currentFollowStatus ||
          state.status.isLoading != isLoading) {
        emit(
          state.copyWith(
            isFollowing: currentFollowStatus,
            status:
                isLoading
                    ? FollowManagementStatus.loading
                    : FollowManagementStatus.success,
            errorMessage: globalState.errorMessage,
          ),
        );
      }
    });
  }

  final GlobalFollowManagementCubit _globalFollowCubit =
      GetIt.instance<GlobalFollowManagementCubit>();

  StreamSubscription? _globalFollowSubscription;

  Future<void> toggleFollow() async {
    final currentlyFollowing = state.isFollowing;
    final userId = state.userId;

    // Delegate to global follow management cubit
    await _globalFollowCubit.toggleFollow(userId, currentlyFollowing);
  }

  void updateFollowStatus(bool isFollowing) {
    // Update global follow status
    _globalFollowCubit.updateFollowStatus(state.userId, isFollowing);
  }

  @override
  Future<void> close() {
    _globalFollowSubscription?.cancel();
    return super.close();
  }
}
