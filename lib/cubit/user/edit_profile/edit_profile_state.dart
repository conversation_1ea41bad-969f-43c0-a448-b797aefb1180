part of 'edit_profile_cubit.dart';

enum EditProfileStatus { initial, loading, success, failure }

extension EditProfileStatusX on EditProfileStatus {
  bool get isInitial => this == EditProfileStatus.initial;
  bool get isLoading => this == EditProfileStatus.loading;
  bool get isSuccess => this == EditProfileStatus.success;
  bool get isFailure => this == EditProfileStatus.failure;
}

final class EditProfileState extends Equatable {
  final EditProfileStatus status;
  final String? errorMessage;
  final String? username;
  final String? displayName;
  final String? bio;
  final String? avatar;
  final String? backgroundImage;
  final bool hasChanges;

  const EditProfileState({
    this.status = EditProfileStatus.initial,
    this.errorMessage,
    this.username,
    this.displayName,
    this.bio,
    this.avatar,
    this.backgroundImage,
    this.hasChanges = false,
  });

  EditProfileState copyWith({
    EditProfileStatus? status,
    String? errorMessage,
    String? username,
    String? displayName,
    String? bio,
    String? avatar,
    String? backgroundImage,
    bool? hasChanges,
  }) {
    return EditProfileState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      bio: bio ?? this.bio,
      avatar: avatar ?? this.avatar,
      backgroundImage: backgroundImage ?? this.backgroundImage,
      hasChanges: hasChanges ?? this.hasChanges,
    );
  }

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    username,
    displayName,
    bio,
    avatar,
    backgroundImage,
    hasChanges,
  ];
}
