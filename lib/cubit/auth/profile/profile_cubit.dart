import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/credentials/credentials.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(const ProfileState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void getProfile() async {
    try {
      final result = await _authRepository.getProfile();
      KeychainService.instance.setProfile(result.data);
      KeychainService.instance.savePrivateKeyToiCloud(credentials: Credentials(
        address: result.data.address ?? "",
        privateKeyHex:  "",
      ));
      emit(
        state.copyWith(status: ProfileStatus.success, userModel: result.data),
      );
    } on DioException catch (e) {
      print('ProfileCubit: getProfile() DioException: ${e.toString()}');
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      print('ProfileCubit: getProfile() Exception: Something went wrong');
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
