import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/cubit/user/global_follow_management/global_follow_management_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(const HomeState()) {
    // Listen to global follow state changes and update posts accordingly
    _globalFollowSubscription = _globalFollowCubit.stream.listen((globalState) {
      _updatePostsWithFollowStatus(globalState.followStatusMap);
    });
  }

  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  final GlobalFollowManagementCubit _globalFollowCubit =
      GetIt.instance<GlobalFollowManagementCubit>();

  StreamSubscription? _globalFollowSubscription;
  int limit = 100;
  void getUserFeed({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        emit(
          state.copyWith(
            status: HomeStatus.loading,
            posts: [],
            canLoadMore: true,
          ),
        );
      } else {
        emit(state.copyWith(status: HomeStatus.loading));
      }
      emit(state.copyWith(status: HomeStatus.loading));

      final result = await _socialRepository.getUserFeed(
        state.posts.lastOrNull?.id,
        limit,
      );
      final data = [...state.posts, ...result.data.posts];

      // Initialize follow statuses from the new posts
      _initializeFollowStatuses(result.data.posts);

      if (result.data.posts.length < limit) {
        emit(
          state.copyWith(
            status: HomeStatus.success,
            posts: data,
            canLoadMore: false,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: HomeStatus.success,
            posts: result.data.posts,
            canLoadMore: true,
          ),
        );
      }
    } on DioException catch (e) {
      emit(
        state.copyWith(status: HomeStatus.failure, errorMessage: e.toString()),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: HomeStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void getUserFeedFollowing({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        emit(
          state.copyWith(
            followingStatus: FollowingStatus.loading,
            followingPosts: [],
            canLoadMoreFollowing: true,
          ),
        );
      } else {
        emit(state.copyWith(followingStatus: FollowingStatus.loading));
      }

      final result = await _socialRepository.getUserFeedFollowing(
        state.followingPosts.lastOrNull?.id,
        limit,
      );
      final data = [...state.followingPosts, ...result.data.posts];

      // Initialize follow statuses from the new posts
      _initializeFollowStatuses(result.data.posts);

      if (result.data.posts.length < limit) {
        emit(
          state.copyWith(
            followingStatus: FollowingStatus.success,
            followingPosts: data,
            canLoadMoreFollowing: false,
          ),
        );
      } else {
        emit(
          state.copyWith(
            followingStatus: FollowingStatus.success,
            followingPosts: data,
            canLoadMoreFollowing: true,
          ),
        );
      }
    } on DioException catch (e) {
      emit(
        state.copyWith(
          followingStatus: FollowingStatus.failure,
          followingErrorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          followingStatus: FollowingStatus.failure,
          followingErrorMessage: "Something went wrong",
        ),
      );
    }
  }

  void addPost(PostModel post) {
    final List<PostModel> updatedPosts = List.from(state.posts)
      ..insert(0, post);
    final List<PostModel> updatedFollowingPosts = List.from(
      state.followingPosts,
    )..insert(0, post);
    emit(
      state.copyWith(
        posts: updatedPosts,
        followingPosts: updatedFollowingPosts,
      ),
    );
  }

  void updatePost(PostModel updatedPost) {
    final List<PostModel> updatedPosts =
        state.posts
            .map((post) => post.id == updatedPost.id ? updatedPost : post)
            .toList();
    final List<PostModel> updatedFollowingPosts =
        state.followingPosts
            .map((post) => post.id == updatedPost.id ? updatedPost : post)
            .toList();
    emit(
      state.copyWith(
        posts: updatedPosts,
        followingPosts: updatedFollowingPosts,
      ),
    );
  }

  void updatePostLikeCount(String postId, int newLikeCount, bool isLiked) {
    // Helper function to update a post's like count
    PostModel updatePostReactions(PostModel post) {
      if (post.id != postId) return post;

      // Update reactions count for 'love' type
      final updatedReactions =
          post.reactions?.reactions?.map((reaction) {
            return reaction.type?.toLowerCase() == 'love'
                ? reaction.copyWith(count: newLikeCount)
                : reaction;
          }).toList() ??
          [];

      // Update user reactions
      final currentUserReactions = post.reactions?.userReactions ?? [];
      final updatedUserReactions =
          isLiked
              ? [...currentUserReactions, 'love']
              : currentUserReactions.where((r) => r != 'love').toList();

      return post.copyWith(
        reactions: post.reactions?.copyWith(
          reactions: updatedReactions,
          userReactions: updatedUserReactions.cast<String>(),
        ),
      );
    }

    final updatedPosts = state.posts.map(updatePostReactions).toList();
    final updatedFollowingPosts =
        state.followingPosts.map(updatePostReactions).toList();

    emit(
      state.copyWith(
        posts: updatedPosts,
        followingPosts: updatedFollowingPosts,
      ),
    );
  }

  /// Update posts with current follow status from global state
  void _updatePostsWithFollowStatus(Map<String, bool> followStatusMap) {
    bool hasChanges = false;

    // Update main posts
    final updatedPosts =
        state.posts.map((post) {
          if (post.user?.id != null &&
              followStatusMap.containsKey(post.user!.id)) {
            final newFollowStatus = followStatusMap[post.user!.id]!;
            if (post.relationship?.isFollowing != newFollowStatus) {
              hasChanges = true;
              return _updatePostFollowStatus(post, newFollowStatus);
            }
          }
          return post;
        }).toList();

    // Update following posts
    final updatedFollowingPosts =
        state.followingPosts.map((post) {
          if (post.user?.id != null &&
              followStatusMap.containsKey(post.user!.id)) {
            final newFollowStatus = followStatusMap[post.user!.id]!;
            if (post.relationship?.isFollowing != newFollowStatus) {
              hasChanges = true;
              return _updatePostFollowStatus(post, newFollowStatus);
            }
          }
          return post;
        }).toList();

    // Only emit if there are actual changes
    if (hasChanges) {
      emit(
        state.copyWith(
          posts: updatedPosts,
          followingPosts: updatedFollowingPosts,
        ),
      );
    }
  }

  /// Helper method to update a post's follow status
  PostModel _updatePostFollowStatus(PostModel post, bool isFollowing) {
    final updatedRelationship = RelationShip(
      isFollowing: isFollowing,
      isFriend: post.relationship?.isFriend,
      isSelf: post.relationship?.isSelf,
    );

    return post.copyWith(relationship: updatedRelationship);
  }

  /// Initialize follow statuses from posts data
  void _initializeFollowStatuses(List<PostModel> posts) {
    final followStatuses = <String, bool>{};

    for (final post in posts) {
      if (post.user?.id != null && post.relationship?.isFollowing != null) {
        followStatuses[post.user!.id] = post.relationship!.isFollowing!;
      }
      // Also check original post for reposts
      if (post.originalPost?.user?.id != null &&
          post.originalPost?.relationship?.isFollowing != null) {
        followStatuses[post.originalPost!.user!.id] =
            post.originalPost!.relationship!.isFollowing!;
      }
    }

    if (followStatuses.isNotEmpty) {
      _globalFollowCubit.batchUpdateFollowStatuses(followStatuses);
    }
  }

  @override
  Future<void> close() {
    _globalFollowSubscription?.cancel();
    return super.close();
  }
}
