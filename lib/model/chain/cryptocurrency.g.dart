// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cryptocurrency.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CryptocurrencyItem _$CryptocurrencyItemFromJson(Map<String, dynamic> json) =>
    CryptocurrencyItem(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      symbol: json['symbol'] as String?,
      slug: json['slug'] as String?,
      numMarketPairs: (json['num_market_pairs'] as num?)?.toInt(),
      dateAdded:
          json['date_added'] == null
              ? null
              : DateTime.parse(json['date_added'] as String),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      maxSupply: (json['max_supply'] as num?)?.toInt(),
      circulatingSupply: (json['circulating_supply'] as num?)?.toDouble(),
      totalSupply: (json['total_supply'] as num?)?.toDouble(),
      platform:
          json['platform'] == null
              ? null
              : PlatformModel.fromJson(
                json['platform'] as Map<String, dynamic>,
              ),
      cmcRank: (json['cmc_rank'] as num?)?.toInt(),
      selfReportedCirculatingSupply: json['self_reported_circulating_supply'],
      selfReportedMarketCap: json['self_reported_market_cap'],
      tvlRatio: json['tvl_ratio'],
      lastUpdated:
          json['last_updated'] == null
              ? null
              : DateTime.parse(json['last_updated'] as String),
      quoteModel:
          json['quote'] == null
              ? null
              : QuoteModel.fromJson(json['quote'] as Map<String, dynamic>),
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$CryptocurrencyItemToJson(
  CryptocurrencyItem instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'symbol': instance.symbol,
  'slug': instance.slug,
  'num_market_pairs': instance.numMarketPairs,
  'date_added': instance.dateAdded?.toIso8601String(),
  'tags': instance.tags,
  'max_supply': instance.maxSupply,
  'circulating_supply': instance.circulatingSupply,
  'total_supply': instance.totalSupply,
  'platform': instance.platform,
  'cmc_rank': instance.cmcRank,
  'self_reported_circulating_supply': instance.selfReportedCirculatingSupply,
  'self_reported_market_cap': instance.selfReportedMarketCap,
  'tvl_ratio': instance.tvlRatio,
  'last_updated': instance.lastUpdated?.toIso8601String(),
  'quote': instance.quoteModel,
  'balance': instance.balance,
};

PlatformModel _$PlatformModelFromJson(Map<String, dynamic> json) =>
    PlatformModel(tokenAdress: json['token_address'] as String?);

Map<String, dynamic> _$PlatformModelToJson(PlatformModel instance) =>
    <String, dynamic>{'token_address': instance.tokenAdress};

UsdModel _$UsdModelFromJson(Map<String, dynamic> json) => UsdModel(
  price: (json['price'] as num?)?.toDouble(),
  volume24h: (json['volume_24h'] as num?)?.toDouble(),
  volumeChange24h: (json['volume_change_24h'] as num?)?.toDouble(),
  percentChange1h: (json['percent_change_1h'] as num?)?.toDouble(),
  percentChange24h: (json['percent_change_24h'] as num?)?.toDouble(),
  percentChange7d: (json['percent_change_7d'] as num?)?.toDouble(),
  percentChange30d: (json['percent_change_30d'] as num?)?.toDouble(),
  percentChange60d: (json['percent_change_60d'] as num?)?.toDouble(),
  percentChange90d: (json['percent_change_90d'] as num?)?.toDouble(),
  marketCap: (json['market_cap'] as num?)?.toDouble(),
  marketCapDominance: (json['market_cap_dominance'] as num?)?.toDouble(),
  fullyDilutedMarketCap: (json['fully_diluted_market_cap'] as num?)?.toDouble(),
  tvl: json['tvl'],
  lastUpdated:
      json['last_updated'] == null
          ? null
          : DateTime.parse(json['last_updated'] as String),
);

Map<String, dynamic> _$UsdModelToJson(UsdModel instance) => <String, dynamic>{
  'price': instance.price,
  'volume_24h': instance.volume24h,
  'volume_change_24h': instance.volumeChange24h,
  'percent_change_1h': instance.percentChange1h,
  'percent_change_24h': instance.percentChange24h,
  'percent_change_7d': instance.percentChange7d,
  'percent_change_30d': instance.percentChange30d,
  'percent_change_60d': instance.percentChange60d,
  'percent_change_90d': instance.percentChange90d,
  'market_cap': instance.marketCap,
  'market_cap_dominance': instance.marketCapDominance,
  'fully_diluted_market_cap': instance.fullyDilutedMarketCap,
  'tvl': instance.tvl,
  'last_updated': instance.lastUpdated?.toIso8601String(),
};

QuoteModel _$QuoteModelFromJson(Map<String, dynamic> json) => QuoteModel(
  usd:
      json['USD'] == null
          ? null
          : UsdModel.fromJson(json['USD'] as Map<String, dynamic>),
);

Map<String, dynamic> _$QuoteModelToJson(QuoteModel instance) =>
    <String, dynamic>{'USD': instance.usd};
