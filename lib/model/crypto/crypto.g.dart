// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crypto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Crypto _$CryptoFromJson(Map<String, dynamic> json) => Crypto(
  nativeCurrency: NativeCurrency.fromJson(
    json['nativeCurrency'] as Map<String, dynamic>,
  ),
  name: json['name'] as String,
  chain: json['chain'] as String?,
  networkId: (json['networkId'] as num?)?.toInt(),
  icon: json['icon'] as String?,
  chainId: (json['chainId'] as num?)?.toInt(),
  rpcUrls: (json['rpc'] as List<dynamic>?)?.map((e) => e as String).toList(),
  contractAddress: json['contractAddress'] as String?,
  canDisplay: json['canDisplay'] as bool? ?? false,
  valueUsd: (json['valueUsd'] as num?)?.toDouble(),
  rpcValid: json['rpcValid'] as String?,
  infoURL: json['infoURL'] as String?,
  shortName: json['shortName'] as String?,
  type:
      $enumDecodeNullable(_$CryptoTypeEnumMap, json['type']) ??
      CryptoType.native,
  idLogo: (json['idLogo'] as num?)?.toInt() ?? -1,
);

Map<String, dynamic> _$CryptoToJson(Crypto instance) => <String, dynamic>{
  'name': instance.name,
  'chain': instance.chain,
  'icon': instance.icon,
  'chainId': instance.chainId,
  'networkId': instance.networkId,
  'nativeCurrency': instance.nativeCurrency,
  'rpc': instance.rpcUrls,
  'contractAddress': instance.contractAddress,
  'valueUsd': instance.valueUsd,
  'canDisplay': instance.canDisplay,
  'rpcValid': instance.rpcValid,
  'infoURL': instance.infoURL,
  'shortName': instance.shortName,
  'type': _$CryptoTypeEnumMap[instance.type]!,
  'idLogo': instance.idLogo,
};

const _$CryptoTypeEnumMap = {
  CryptoType.native: 'native',
  CryptoType.token: 'token',
};

NativeCurrency _$NativeCurrencyFromJson(Map<String, dynamic> json) =>
    NativeCurrency(
      name: json['name'] as String,
      symbol: json['symbol'] as String,
      decimals: (json['decimals'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NativeCurrencyToJson(NativeCurrency instance) =>
    <String, dynamic>{
      'name': instance.name,
      'symbol': instance.symbol,
      'decimals': instance.decimals,
    };
