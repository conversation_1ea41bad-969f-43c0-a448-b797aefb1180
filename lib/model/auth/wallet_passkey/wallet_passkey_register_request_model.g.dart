// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_passkey_register_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WalletPasskeyRegisterRequestModel _$WalletPasskeyRegisterRequestModelFromJson(
  Map<String, dynamic> json,
) => WalletPasskeyRegisterRequestModel(
  address: json['address'] as String?,
  signature: json['signature'] as String?,
  attestationInfo:
      json['attestationInfo'] == null
          ? null
          : WebAuthnResponseModel.fromJson(
            json['attestationInfo'] as Map<String, dynamic>,
          ),
  assertionInfo:
      json['assertionInfo'] == null
          ? null
          : WebAuthnResponseModel.fromJson(
            json['assertionInfo'] as Map<String, dynamic>,
          ),
  userId: json['userId'] as String?,
);

Map<String, dynamic> _$WalletPasskeyRegisterRequestModelToJson(
  WalletPasskeyRegisterRequestModel instance,
) => <String, dynamic>{
  'address': instance.address,
  'signature': instance.signature,
  'attestationInfo': instance.attestationInfo,
  'assertionInfo': instance.assertionInfo,
  'userId': instance.userId,
};
