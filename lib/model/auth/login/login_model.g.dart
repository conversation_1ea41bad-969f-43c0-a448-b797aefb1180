// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequestModel _$LoginRequestModelFromJson(Map<String, dynamic> json) =>
    LoginRequestModel(
      email: json['email'] as String?,
      password: json['password'] as String?,
      appType: json['appType'] as String? ?? "toii-social",
      phoneNumber: json['phoneNumber'] as String?,
    );

Map<String, dynamic> _$LoginRequestModelToJson(LoginRequestModel instance) =>
    <String, dynamic>{
      'appType': instance.appType,
      'email': instance.email,
      'password': instance.password,
      'phoneNumber': instance.phoneNumber,
    };

LoginModel _$LoginModelFromJson(Map<String, dynamic> json) => LoginModel(
  accessToken: json['access_token'] as String,
  expiredAt: json['expired_at'] as String,
  isNewUser: json['is_new_user'] as bool?,
  refreshToken: json['refresh_token'] as String,
  tmpToken: json['tmp_token'] as String?,
);

Map<String, dynamic> _$LoginModelToJson(LoginModel instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'expired_at': instance.expiredAt,
      'is_new_user': instance.isNewUser,
      'refresh_token': instance.refreshToken,
      'tmp_token': instance.tmpToken,
    };
