// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_chat_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiChatRequestModel _$AiChatRequestModelFromJson(Map<String, dynamic> json) =>
    AiChatRequestModel(
      messages:
          (json['messages'] as List<dynamic>?)
              ?.map(
                (e) => AiChatMessageModel.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      includeReasoning: json['include_reasoning'] as bool? ?? false,
      stream: json['stream'] as bool? ?? false,
    );

Map<String, dynamic> _$AiChatRequestModelToJson(AiChatRequestModel instance) =>
    <String, dynamic>{
      'messages': instance.messages,
      'include_reasoning': instance.includeReasoning,
      'stream': instance.stream,
    };

AiChatMessageModel _$AiChatMessageModelFromJson(Map<String, dynamic> json) =>
    AiChatMessageModel(
      role: json['role'] as String?,
      content: json['content'] as String?,
    );

Map<String, dynamic> _$AiChatMessageModelToJson(AiChatMessageModel instance) =>
    <String, dynamic>{'role': instance.role, 'content': instance.content};
