// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_chat_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiChatResponseModel _$AiChatResponseModelFromJson(
  Map<String, dynamic> json,
) => AiChatResponseModel(
  id: json['id'] as String?,
  object: json['object'] as String?,
  created: (json['created'] as num?)?.toInt(),
  model: json['model'] as String?,
  choices:
      (json['choices'] as List<dynamic>?)
          ?.map((e) => AiChatChoiceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  usage:
      json['usage'] == null
          ? null
          : AiChatUsageModel.fromJson(json['usage'] as Map<String, dynamic>),
);

Map<String, dynamic> _$AiChatResponseModelToJson(
  AiChatResponseModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'object': instance.object,
  'created': instance.created,
  'model': instance.model,
  'choices': instance.choices,
  'usage': instance.usage,
};

AiChatChoiceModel _$AiChatChoiceModelFromJson(Map<String, dynamic> json) =>
    AiChatChoiceModel(
      index: (json['index'] as num?)?.toInt(),
      message:
          json['message'] == null
              ? null
              : AiChatMessageResponseModel.fromJson(
                json['message'] as Map<String, dynamic>,
              ),
      finishReason: json['finish_reason'] as String?,
    );

Map<String, dynamic> _$AiChatChoiceModelToJson(AiChatChoiceModel instance) =>
    <String, dynamic>{
      'index': instance.index,
      'message': instance.message,
      'finish_reason': instance.finishReason,
    };

AiChatMessageResponseModel _$AiChatMessageResponseModelFromJson(
  Map<String, dynamic> json,
) => AiChatMessageResponseModel(
  role: json['role'] as String?,
  content: json['content'] as String?,
);

Map<String, dynamic> _$AiChatMessageResponseModelToJson(
  AiChatMessageResponseModel instance,
) => <String, dynamic>{'role': instance.role, 'content': instance.content};

AiChatUsageModel _$AiChatUsageModelFromJson(Map<String, dynamic> json) =>
    AiChatUsageModel(
      promptTokens: (json['prompt_tokens'] as num?)?.toInt(),
      completionTokens: (json['completion_tokens'] as num?)?.toInt(),
      totalTokens: (json['total_tokens'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AiChatUsageModelToJson(AiChatUsageModel instance) =>
    <String, dynamic>{
      'prompt_tokens': instance.promptTokens,
      'completion_tokens': instance.completionTokens,
      'total_tokens': instance.totalTokens,
    };
