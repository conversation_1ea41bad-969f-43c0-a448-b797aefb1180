// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  id: json['id'] as String,
  username: json['username'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  email: json['email'] as String?,
  firstName: json['first_name'] as String?,
  lastName: json['last_name'] as String?,
  fullName: json['full_name'] as String?,
  avatar: json['avatar'] as String?,
  address: json['address'] as String?,
  bio: json['bio'] as String?,
  lastActivity:
      json['last_activity'] == null
          ? null
          : DateTime.parse(json['last_activity'] as String),
  role: json['role'] as String?,
  status: json['status'] as String?,
  createdAt:
      json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
  updatedAt:
      json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
  deletedAt:
      json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
  walletAddress: json['wallet_address'] as String?,
  followersCount: (json['followers_count'] as num?)?.toInt(),
  followingCount: (json['following_count'] as num?)?.toInt(),
  postsCount: (json['posts_count'] as num?)?.toInt(),
  isFollowed: json['is_followed'] as bool?,
  avatarUrl: json['avatar_url'] as String?,
  backgroundUrl: json['background_url'] as String?,
  isVerified: json['is_verified'] as bool? ?? false,
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'phoneNumber': instance.phoneNumber,
  'email': instance.email,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'full_name': instance.fullName,
  'avatar': instance.avatar,
  'address': instance.address,
  'wallet_address': instance.walletAddress,
  'bio': instance.bio,
  'last_activity': instance.lastActivity?.toIso8601String(),
  'role': instance.role,
  'status': instance.status,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
  'deleted_at': instance.deletedAt?.toIso8601String(),
  'followers_count': instance.followersCount,
  'following_count': instance.followingCount,
  'posts_count': instance.postsCount,
  'is_followed': instance.isFollowed,
  'avatar_url': instance.avatarUrl,
  'background_url': instance.backgroundUrl,
  'is_verified': instance.isVerified,
};
