import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';
import 'package:toii_social/model/ai_chat/ai_chat_response_model.dart';

part 'ai_chat_service.g.dart';

@RestApi()
abstract class AiChatService {
  factory AiChatService(Dio dio, {String baseUrl}) = _AiChatService;

  @POST('https://j0cgncj6iimfew-8000.proxy.runpod.net/v1/chat/completions')
  Future<AiChatResponseModel> sendMessage(
    @Body() AiChatRequestModel request,
    @Header('Authorization') String authorization,
  );
}
