import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:passkeys/types.dart';
import 'package:toii_social/core/service/pass_key/auth_passkey_service.dart';
import 'package:toii_social/flavors/flavors.dart';

class LocalUser {
  LocalUser({
    required this.name,
    required this.id,
    this.credentialID,
    required this.transports,
  });

  final String name;
  final String id;
  String? credentialID;
  List<String> transports;
}

var rpID =
    kIsWeb
        ? ((kDebugMode) ? 'localhost' : F.rpIDWeb)
        : 'app.gao.social';

class MalformedBase64UrlCredentialID implements Exception {
  final String message;
  MalformedBase64UrlCredentialID([
    this.message = 'Credential ID is not a valid Base64URL-encoded string',
  ]);
  @override
  String toString() => 'MalformedBase64UrlCredentialID: $message';
}

/// This is a local version of a relying party server.
///
/// Note:
/// The concept of a local relying party server works just in examples. It does not work in practice.
/// Usually a relying party server is party of your backend.
class LocalRelyingPartyServer {
  LocalRelyingPartyServer();

  final Map<String, LocalUser> _users = HashMap();
  final Map<String, LocalUser> _inFlightChallenges = HashMap();
  final Random _random = Random();

  RegisterRequestType startPasskeyRegister({
    required String name,
    Configuration? configuration,
    String? challenge,
  }) {
    // Check if user exists and handle appropriately
    if (_users.containsKey(name)) {
      print('User $name already exists, attempting re-registration');
      // Allow re-registration by removing the existing user
      _users.remove(name);
      // Also remove any in-flight challenges for this user
      _inFlightChallenges.removeWhere((key, value) => value.name == name);
    }

    final userID = 'user-${_random.nextInt(100000000)}';
    final newUser = LocalUser(id: userID, name: name, transports: []);
    final challenge1 = challenge ?? generateChallenge();
    _inFlightChallenges[challenge1] = newUser;

    final rp = RelyingPartyType(name: 'local-relying-party-server', id: rpID);
    final user = UserType(
      displayName: name,
      name: name,
      id: base64Url.encode(name.codeUnits),
    );
    final authenticatorSelection = AuthenticatorSelectionType(
      requireResidentKey: false,
      residentKey: 'required',
      userVerification: 'preferred',
    );
    final credentialId = base64Url.encode(name.codeUnits).replaceAll('=', '');
    final exclude = buildExcludeCredentials([credentialId]);

    return RegisterRequestType(
      challenge: challenge1,
      relyingParty: rp,
      user: user,
      authSelectionType: authenticatorSelection,
      pubKeyCredParams: [
        PubKeyCredParamType(type: 'public-key', alg: -7),
        PubKeyCredParamType(type: 'public-key', alg: -257),
      ],
      excludeCredentials: exclude,
      // configuration?.excludeCredentials == true
      //     ? _users.values
      //         .map(
      //           (e) => CredentialType(
      //             type: 'public-key',
      //             id: e.credentialID!,
      //             transports: ['internal'],
      //           ),
      //         )
      //         .toList()
      //     : [],
      timeout: configuration?.timeout ?? 60000,
    );
  }

  /// Note: we don't implement a full relying party server here.
  /// To safe effort we don't verify the response from the authenticator.
  LocalUser finishPasskeyRegister({required RegisterResponseType response}) {
    final raw = addBase64Padding(response.clientDataJSON);
    final json = jsonDecode(String.fromCharCodes(base64.decode(raw)));

    final challenge = json['challenge'];
    final user = _inFlightChallenges[challenge];
    if (user == null) {
      throw Exception('invalid state: user does not exist');
    }

    user
      ..credentialID = response.id
      ..transports =
          response.transports.isEmpty
              // When using FaceID or TouchID, the transports are empty.
              ? ['internal', 'hybrid']
              : response.transports as List<String>;
    _users[user.name] = user;

    return user;
  }

  AuthenticateRequestType startPasskeyLogin({
    required String name,
    Configuration? configuration,
    required String challenge,
  }) {
    // if (!_users.containsKey(name)) {
    //   throw Exception('User $name does not exist. Please register first');
    // }

    // final challenge = generateChallenge();
    // _inFlightChallenges[challenge] = _users[name]!;

    // Validate credential ID

    return AuthenticateRequestType(
      relyingPartyId: rpID,
      challenge: challenge,
      mediation: MediationType.Optional,
      userVerification: 'preferred',
      preferImmediatelyAvailableCredentials: false,
      // preferImmediatelyAvailableCredentials:
      //     configuration?.preferImmediatelyAvailableCredentials == true
      //         ? true
      //         : false,
      // allowCredentials: [
      //   CredentialType(
      //     type: 'public-key',
      //     id:  base64Url.encode(fromBase64UrlNoPad(name).codeUnits), name,
      //     transports: ['internal'],
      //   ),
      // ],
      // allowCredentials: configuration?.allowCredentials == true
      //     ? [
      //         CredentialType(
      //           type: 'public-key',
      //           id: 'id',
      //           transports: ['internal'],
      //         )
      //       ]
      //     : _users[name]!.credentialID != null
      //         ? [
      //             CredentialType(
      //               type: 'public-key',
      //               id: _users[name]!.credentialID!,
      //               transports: _users[name]!.transports,
      //             ),
      //           ]
      //         : null,
      timeout: configuration?.timeout ?? 60000,
    );
  }

  /// Note: we don't implement a full relying party server here.
  /// To safe effort we don't verify the response from the authenticator.
  LocalUser finishPasskeyLogin({required AuthenticateResponseType response}) {
    final raw = addBase64Padding(response.clientDataJSON);
    final json = jsonDecode(String.fromCharCodes(base64.decode(raw)));

    final challenge = json['challenge'];
    final user = _inFlightChallenges[challenge];
    if (user == null) {
      throw Exception('invalid state: user does not exist');
    }

    return user;
  }

  AuthenticateRequestType startPasskeyLoginConditionalU({
    required String challenge,
  }) {
    // final challenge = generateChallenge();

    return AuthenticateRequestType(
      relyingPartyId: rpID,
      challenge: challenge,
      mediation: MediationType.Conditional,
      preferImmediatelyAvailableCredentials: false,
    );
  }

  /// Note: we don't implement a full relying party server here.
  /// To safe effort we don't verify the response from the authenticator.
  LocalUser finishPasskeyLoginConditionalUI({
    required AuthenticateResponseType response,
  }) {
    LocalUser? existingUser;
    for (final user in _users.values) {
      if (user.credentialID != null && user.credentialID == response.id) {
        existingUser = user;
      }
    }

    if (existingUser == null) {
      throw Exception('User does not exist');
    }

    return existingUser;
  }

  String generateChallenge() {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var rawChallenge = '';
    for (var i = 0; i < 32; i++) {
      rawChallenge += chars[_random.nextInt(chars.length)];
    }

    final a = base64Url.encode(rawChallenge.codeUnits);

    return a.substring(0, a.length - 1);
  }

  String addBase64Padding(String base64String) {
    final missingPadding = (4 - (base64String.length % 4)) % 4;
    return base64String + ('=' * missingPadding);
  }
}

Uint8List fromBase64UrlNoPad(String s) {
  final norm =
      s.replaceAll('-', '+').replaceAll('_', '/') +
      '=' * ((4 - s.length % 4) % 4);
  return Uint8List.fromList(base64.decode(norm));
}

List<CredentialType> buildExcludeCredentials(List<String> credentialIds) {
  return credentialIds.map((id) {
    // Validate that the credential ID is a valid Base64URL string without padding
    if (!RegExp(r'^[A-Za-z0-9_-]+$').hasMatch(id)) {
      throw MalformedBase64UrlCredentialID('Invalid Base64URL format: $id');
    }

    return CredentialType(
      type: "public-key",
      id: id,
      transports: ["internal"], // hoặc ["internal","hybrid"], tùy nhu cầu
    );
  }).toList();
}
