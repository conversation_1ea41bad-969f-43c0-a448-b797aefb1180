import 'package:passkeys/authenticator.dart';
import 'package:passkeys/types.dart';
import 'package:toii_social/core/service/pass_key/local_relying_party_server.dart';

class Configuration {
  Configuration({
    required this.name,
    this.timeout,
    this.excludeCredentials,
    this.allowCredentials,
    this.preferImmediatelyAvailableCredentials,
  });

  final String name;
  final int? timeout;
  final bool? excludeCredentials;
  final bool? allowCredentials;
  final bool? preferImmediatelyAvailableCredentials;
}

List<Configuration> SIGNUP_ANDROID_CONFIGURATIONS = [
  Configuration(name: 'Default'),
  Configuration(name: '5s Timeout', timeout: 5000),
  Configuration(name: 'ExcludeCredentials', excludeCredentials: true),
];

List<Configuration> SIGNUP_IOS_CONFIGURATIONS = [
  Configuration(name: 'Default'),
  Configuration(name: 'ExcludeCredentials', excludeCredentials: true),
];

List<Configuration> LOGIN_ANDROID_CONFIGURATIONS = [
  Configuration(name: 'Default'),
  Configuration(name: '5s Timeout', timeout: 5000),
  Configuration(name: 'AllowCredentials', allowCredentials: true),
  Configuration(
    name: 'PreferImmediatelyAvailableCredentials',
    preferImmediatelyAvailableCredentials: true,
    allowCredentials: true,
  ),
];

List<Configuration> LOGIN_IOS_CONFIGURATIONS = [
  Configuration(name: 'Default'),
  Configuration(
    name: 'AllowCredentials And PreferImmediatelyAvailableCredentials',
    allowCredentials: true,
    preferImmediatelyAvailableCredentials: true,
  ),
];

class AuthPassKeyService {
  AuthPassKeyService({required this.rps, required this.authenticator});

  final LocalRelyingPartyServer rps;
  final PasskeyAuthenticator authenticator;

  Future<AuthenticateResponseType> loginWithPasskey({
    required String email,
    required String challenge,
  }) async {
    try {
      final rps1 = rps.startPasskeyLogin(
        name: email,
        configuration: loginConfiguration,
        challenge: challenge,
      );
      final authenticatorRes = await authenticator.authenticate(rps1);
      // rps.finishPasskeyLogin(response: authenticatorRes);
      return authenticatorRes;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthenticateResponseType> loginWithPasskeyConditionalUI({
    required String challenge,
  }) async {
    try {
      final rps1 = rps.startPasskeyLoginConditionalU(challenge: challenge);
      final authenticatorRes = await authenticator.authenticate(rps1);
      // rps.finishPasskeyLoginConditionalUI(response: authenticatorRes);
      return authenticatorRes;
    } catch (e) {
      print(e);
      rethrow;
    }
  }

  Future<RegisterResponseType> signupWithPasskey({
    required String email,
    required String challenge,
  }) async {
    try {
      final rps1 = rps.startPasskeyRegister(
        name: email,
        configuration: signUpConfiguration,
        challenge: challenge,
      );
      final authenticatorRes = await authenticator.register(rps1);
      rps.finishPasskeyRegister(response: authenticatorRes);
      return authenticatorRes;
    } catch (e) {
      // Handle specific passkey registration errors
      if (e.toString().contains('TYPE_NOT_ALLOWED_ERROR') ||
          e.toString().contains('UnhandledAuthenticatorException')) {
        throw Exception(
          'Passkey registration not allowed: User denied or passkey already exists',
        );
      } else if (e.toString().contains('TYPE_INVALID_STATE_ERROR')) {
        throw Exception('Invalid passkey registration state');
      } else if (e.toString().contains('TYPE_SECURITY_ERROR')) {
        throw Exception('Passkey registration security error');
      } else {
        // Re-throw with more context
        throw Exception('Passkey registration failed: ${e.toString()}');
      }
    }
  }

  // GetAvailability getAvailability() {
  //   return authenticator.getAvailability();
  // }

  // This is only related to testing and testing configurations.
  Configuration? signUpConfiguration;
  Configuration? loginConfiguration;

  void setSignupConfiguration(Configuration? configuration) {
    signUpConfiguration = configuration;
  }

  void setLoginConfiguration(Configuration? configuration) {
    loginConfiguration = configuration;
  }
}
