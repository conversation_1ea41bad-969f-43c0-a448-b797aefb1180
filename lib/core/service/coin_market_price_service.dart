//https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest?symbol=ETH,BTC

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/chain/listings_model.dart';

part 'coin_market_price_service.g.dart';

@RestApi()
abstract class CoinMarketPriceService {
  factory CoinMarketPriceService(Dio dio, {String baseUrl}) =
      _CoinMarketPriceService;

  @GET("/cryptocurrency/quotes/latest")
  Future<ListingsModel> getInfoCoin(
    @Header('X-CMC_PRO_API_KEY') String apiKey,
    @Query('symbol') String listToken,
  );

  @GET("/auth/api/v1/crypto/quotes")
  Future<ListingsModel> getListingLatest(
  );
}