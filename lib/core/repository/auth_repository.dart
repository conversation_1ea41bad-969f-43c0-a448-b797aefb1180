import 'package:toii_social/core/service/auth_service.dart';
import 'package:toii_social/model/auth/attestation_nonce/attestation_nonce_model.dart';
import 'package:toii_social/model/auth/change_password/change_password_request_model.dart';
import 'package:toii_social/model/auth/change_password/change_password_response_model.dart';
import 'package:toii_social/model/auth/login/login_model.dart';
import 'package:toii_social/model/auth/login_gmail/login_gmail_request_model.dart';
import 'package:toii_social/model/auth/login_wallet/login_wallet.dart';
import 'package:toii_social/model/auth/otp/request_otp.dart';
import 'package:toii_social/model/auth/register/verify_otp_model.dart';
import 'package:toii_social/model/auth/register_update_info/register_update_info_model.dart';
import 'package:toii_social/model/auth/username_availability/username_availability_response_model.dart';
import 'package:toii_social/model/auth/wallet/wallet_message_auth_model.dart';
import 'package:toii_social/model/auth/wallet_nonce/wallet_nonce_model.dart';
import 'package:toii_social/model/auth/wallet_passkey/wallet_passkey_register_request_model.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/user/user_model.dart';

abstract class AuthRepository {
  Future<BaseResponse<LoginModel>> login(LoginRequestModel request);
  Future<BaseResponse<UserModel>> getProfile();
  Future<BaseResponse<void>> requestOtp(RequestOtpModel request);
  Future<BaseResponse<VerifyOtpResponseModel>> verifyOtp(
    RequestVerifyOtpModel request,
  );
  Future<BaseResponse<LoginModel>> completeRegistration(
    RegisterUpdateInfoRequestModel request,
  );
  Future<BaseResponse<LoginModel>> tokenWithGmail(
    LoginGmailRequestModel request,
  );

  Future<BaseResponse<LoginModel>> tokenWithApple(
    LoginAppleRequestModel request,
  );
  Future<BaseResponse<LoginModel>> loginWithWallet(
    WalletLoginRequestModel request,
  );
  Future<BaseResponse<WalletMessageAuthModel>>
  getNonceMessageForWalletSignature(String address);

  /// Change user password
  Future<BaseResponse<ChangePasswordResponseModel>> changePassword(
    ChangePasswordRequestModel request,
  );

  /// Check username availability
  Future<BaseResponse<UsernameAvailabilityResponseModel>>
  checkUsernameAvailability(String username);

  /// Delete user account
  Future<BaseResponse<void>> deleteAccount(String userId);

  /// Logout user by clearing local authentication state
  Future<void> logout();
  Future<BaseResponse<AttestationNonceModel>> getAttestationNonce();
  Future<BaseResponse<LoginModel>> registerAttestation(
    AttestationRegisterRequestModel request,
  );
  Future<BaseResponse<WalletNonceModel>> getWalletNonce(String address);
  Future<BaseResponse<LoginModel>> loginOrRegisterWallet(
    WalletRegisterNonceModel request,
  );

  /// Register wallet with passkey
  Future<BaseResponse<dynamic>> registerWalletPasskey(
    WalletPasskeyRegisterRequestModel request,
  );

  Future<BaseResponse<LoginModel>> loginWithPasskey(
    WalletPasskeyRegisterRequestModel request,
  );

  Future<List<UserModel>> getProfileByPassKey(String passkey);
}

class AuthRepositoryImpl extends AuthRepository {
  final AuthService authService;
  AuthRepositoryImpl({required this.authService});

  @override
  Future<BaseResponse<LoginModel>> login(LoginRequestModel request) async {
    try {
      return await authService.login(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<UserModel>> getProfile() async {
    try {
      return await authService.getProfile();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<void>> requestOtp(RequestOtpModel request) async {
    try {
      return await authService.requestOtp(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<VerifyOtpResponseModel>> verifyOtp(
    RequestVerifyOtpModel request,
  ) async {
    try {
      return await authService.verifyOtp(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> completeRegistration(
    RegisterUpdateInfoRequestModel request,
  ) async {
    try {
      return await authService.completeRegistration(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> tokenWithGmail(
    LoginGmailRequestModel request,
  ) async {
    try {
      return await authService.tokenWithGmail(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> tokenWithApple(
    LoginAppleRequestModel request,
  ) async {
    try {
      return await authService.tokenWithApple(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> loginWithWallet(
    WalletLoginRequestModel request,
  ) async {
    try {
      return await authService.loginWallet(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<WalletMessageAuthModel>>
  getNonceMessageForWalletSignature(String address) async {
    try {
      return await authService.getNonceMessageForWalletSignature(address);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<ChangePasswordResponseModel>> changePassword(
    ChangePasswordRequestModel request,
  ) async {
    try {
      return await authService.changePassword(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<UsernameAvailabilityResponseModel>>
  checkUsernameAvailability(String username) async {
    try {
      return await authService.checkUsernameAvailability(username);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<void>> deleteAccount(String userId) async {
    try {
      return await authService.deleteAccount(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    // Currently, there's no logout API endpoint
    // This method is implemented for future use when logout API is available
    // For now, the logout logic is handled in the LogoutCubit
    return;
  }

  @override
  Future<BaseResponse<AttestationNonceModel>> getAttestationNonce() async {
    try {
      return await authService.getAttestationNonce();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> registerAttestation(
    AttestationRegisterRequestModel request,
  ) async {
    try {
      return await authService.registerAttestation(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<WalletNonceModel>> getWalletNonce(String address) async {
    try {
      return await authService.getWalletNonce(address);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> loginOrRegisterWallet(
    WalletRegisterNonceModel request,
  ) async {
    try {
      return await authService.loginOrRegisterWallet(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<dynamic>> registerWalletPasskey(
    WalletPasskeyRegisterRequestModel request,
  ) async {
    try {
      return await authService.registerWalletPasskey(request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<LoginModel>> loginWithPasskey(
    WalletPasskeyRegisterRequestModel request,
  ) {
    return authService.loginWithPasskey(request);
  }
  
  @override
  Future<List<UserModel>> getProfileByPassKey(String passkey) async {
     var result = await authService.getProfileByPassKey(passkey);
     return result.data.profiles;
  }
}
