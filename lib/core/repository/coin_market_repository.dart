
import 'package:toii_social/core/service/coin_market_price_service.dart';
import 'package:toii_social/model/chain/listings_model.dart';

abstract class CoinMarketRepository {
  Future<ListingsModel> getInfoCoin(
    String listToken,
  );
  Future<ListingsModel> getListingLatest();
}

class CoinMarketRepositoryImpl implements CoinMarketRepository {
  final CoinMarketPriceService coinMarketPriceService;
  CoinMarketRepositoryImpl({required this.coinMarketPriceService});

  @override
  Future<ListingsModel> getInfoCoin(
    String listToken,
  ) async {
    return await coinMarketPriceService.getInfoCoin(
        "981b70de-9d7e-4897-8639-4c7e0692d195", listToken);
  }

  @override
  Future<ListingsModel> getListingLatest() async {
    return await coinMarketPriceService
        .getListingLatest();
  }
}
