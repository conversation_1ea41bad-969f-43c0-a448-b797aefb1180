import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:toii_social/core/service/social_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/model/media/media_model.dart';
import 'package:toii_social/model/post/hide_post_request.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/model/post/post_request.dart';
import 'package:toii_social/model/post/repost_request.dart';

abstract class SocialRepository {
  Future<BaseResponse<PostResponseDataModel>> getUserFeed(
    String? cursor,
    int limit,
  );
  Future<BaseResponse<PostResponseDataModel>> getUserFeedFollowing(
    String? cursor,
    int limit,
  );
  Future<BaseResponse<CommentListModel>> getCommentsOfPost(String id);
  Future<BaseResponse<CommentItemModel>> createCommentsOfPost(
    String id,
    CreateCommentRequestModel comment,
  );

  Future<BaseResponse<PostModel>> createNewPost(CreatePostRequestModel post);
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  );
  Future<BaseResponse<MediaUploadResponseModel>> uploadMediaFromBytes(
    Uint8List bytes,
    String fileName,
    String type,
  );
  Future<void> likePost(String id);
  Future<void> unlikePost(String id);
  Future<BaseResponse<PostModel>> getPostById(String id);
  Future<void> likeComment(String commentId);
  Future<void> unlikeComment(String commentId);
  Future<void> updatePost(String id, PostRequest postRequest);
  Future<void> deletePost(String id);
  Future<void> hidePost(String postId);
  Future<void> repostPost(RepostRequest request);

  Future<BaseResponse<PostResponseDataModel>> getUserPosts(String userId);
  Future<BaseResponse<PostResponseDataModel>> getUserReposts(String userId);
}

class SocialRepositoryImpl extends SocialRepository {
  final SocialService socialService;

  SocialRepositoryImpl({required this.socialService});

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserFeed(
    String? cursor,
    int limit,
  ) {
    return socialService.getUserFeed(cursor, limit);
  }

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserFeedFollowing(
    String? cursor,
    int limit,
  ) {
    return socialService.getUserFeedFollowing(cursor, limit);
  }

  @override
  Future<BaseResponse<CommentListModel>> getCommentsOfPost(String id) {
    return socialService.getCommentsOfPost(id, true);
  }

  @override
  Future<BaseResponse<CommentItemModel>> createCommentsOfPost(
    String id,
    CreateCommentRequestModel comment,
  ) {
    return socialService.createCommentsOfPost(id, comment);
  }

  @override
  Future<BaseResponse<PostModel>> createNewPost(CreatePostRequestModel post) {
    return socialService.createPost(post);
  }

  @override
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  ) async {
    if (kIsWeb) {
      // For web, create FormData manually to avoid Platform.pathSeparator
      final bytes = await file.readAsBytes();
      final fileName = _getFileNameFromPath(file.path);

      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(bytes, filename: fileName),
        'type': type,
      });

      return socialService.uploadMediaWeb(formData);
    } else {
      // For mobile, use the existing method
      return socialService.uploadMedia(file, type);
    }
  }

  // Add new method for web-specific upload using bytes directly
  @override
  Future<BaseResponse<MediaUploadResponseModel>> uploadMediaFromBytes(
    Uint8List bytes,
    String fileName,
    String type,
  ) async {
    final formData = FormData.fromMap({
      'file': MultipartFile.fromBytes(bytes, filename: fileName),
      'type': type,
    });

    return socialService.uploadMediaWeb(formData);
  }

  /// Extract filename from path in a web-safe way
  String _getFileNameFromPath(String path) {
    if (kIsWeb) {
      // On web, paths might be blob URLs or different formats
      if (path.contains('/')) {
        return path.split('/').last;
      }
      return 'upload.jpg'; // Default filename for web
    } else {
      // On mobile, use platform-specific separator
      return path.split(Platform.pathSeparator).last;
    }
  }

  @override
  Future<void> likePost(String id) {
    return socialService.likePost(id, 'love');
  }

  @override
  Future<void> unlikePost(String id) {
    return socialService.unlikePost(id, 'love');
  }

  @override
  Future<BaseResponse<PostModel>> getPostById(String id) {
    return socialService.getPostById(id);
  }

  @override
  Future<void> likeComment(String commentId) {
    return socialService.likeComment(commentId, 'love');
  }

  @override
  Future<void> unlikeComment(String commentId) {
    return socialService.unlikeComment(commentId, 'love');
  }

  @override
  Future<void> updatePost(String id, PostRequest postRequest) {
    return socialService.updatePost(id, postRequest);
  }

  @override
  Future<void> deletePost(String id) {
    return socialService.deletePost(id);
  }

  @override
  Future<void> hidePost(String postId) {
    return socialService.hidePost(HidePostRequest(postId: postId));
  }

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserPosts(String userId) {
    return socialService.getUserPosts(userId);
  }

  @override
  Future<BaseResponse<PostResponseDataModel>> getUserReposts(String userId) {
    return socialService.getUserReposts(userId);
  }

  @override
  Future<void> repostPost(RepostRequest request) {
    return socialService.repostPost(request);
  }
}
