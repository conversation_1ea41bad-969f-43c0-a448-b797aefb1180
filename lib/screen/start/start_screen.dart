import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  Map<String, UserModel> profileData = {};
  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      getData();
    });
  }

  void getData() async {
    try {
      final profile = await KeychainService.instance.getProfile();
      final map = profile.map(
        (key, value) => MapEntry(key, UserModel.fromJson(value)),
      );
      setState(() {
        profileData = map;
      });
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: BlocConsumer<LoginCubit, LoginState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            context.go(RouterEnums.inital.routeName);
          }
          if (state.status.isFailure) {
            context.showSnackbar(message: state.message ?? "");
          }
        },
        builder: (context, state) {
          final profileKeys = profileData.keys.toList();
          return Scaffold(
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            body: Container(
              height: double.infinity,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bgHome.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Assets.icons.newLogo.image(),
                      Expanded(
                        child: GridView.count(
                          childAspectRatio: 1 / 1.5,
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          crossAxisCount: 2,
                          children: List.generate(profileKeys.length, (index) {
                            final walletKey = profileKeys[index];
                            return Builder(
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () {
                                    context.read<LoginCubit>().loginWithPasskey(
                                      userId: profileData[walletKey]?.id ?? '',
                                      userName:
                                          profileData[walletKey]?.username ??
                                          '',
                                    );
                                  },
                                  child: Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        AvatarWidget(
                                          size: 154,
                                          imageUrl:
                                              profileData[walletKey]?.avatarUrl,
                                          name:
                                              profileData[walletKey]
                                                  ?.username ??
                                              '',
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          profileData[walletKey]?.username ??
                                              "",
                                          style: titleMedium.copyColor(
                                            themeData.neutral800,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          }),
                        ),
                      ),
                      _nextButton(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 22),
                    alignment: Alignment.center,
                    height: 48,
                    decoration: BoxDecoration(
                      border: Border.all(color: themeData.primaryGreen500),
                      // color: themeData.black800,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: Text(
                      'Activate Soul',
                      style: titleMedium.copyColor(themeData.primaryGreen500),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Builder(
                builder: (context) {
                  return GestureDetector(
                    onTap: () {
                      context.read<LoginCubit>().getProfileByPassKey();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 22),
                      alignment: Alignment.center,
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(color: themeData.primaryGreen500),
                        // color: themeData.black800,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Text(
                        'Restore',
                        style: titleMedium.copyColor(themeData.primaryGreen500),
                      ),
                    ),
                  );
                }
              ),
            ],
          ),
        ),
      ],
    );
  }
}
