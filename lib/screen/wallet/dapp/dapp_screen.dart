import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class DappScreen extends StatefulWidget {
  const DappScreen({super.key});

  @override
  State<DappScreen> createState() => _DappScreenState();
}

class _DappScreenState extends State<DappScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedCategoryIndex = 0;
  final TextEditingController _searchController = TextEditingController();

  final List<String> categories = ['All', 'DEX', 'NFTs', 'Gaming'];

  final List<DAppItem> dapps = [
    DAppItem(
      name: 'PancakeSwap',
      description: 'The leading DEX on BNB Chain with the bes...',
      icon: '🥞',
      category: 'DEX',
      url: 'https://pancakeswap.finance',
    ),
    DAppItem(
      name: 'Uniswap',
      description: 'A decentralized trading protocol on Ethereu...',
      icon: '🦄',
      category: 'DEX',
      url: 'https://uniswap.org',
    ),
    DAppItem(
      name: '1inch',
      description: '1inch is a DEX aggregator powering flexible ...',
      icon: '🔷',
      category: 'DEX',
      url: 'https://1inch.io',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      title: Text('Browser', style: titleLarge.copyColor(themeData.neutral800)),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Container(
                      decoration: BoxDecoration(
                        color: themeData.neutral100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Enter address',
                          hintStyle: bodyMedium.copyColor(themeData.neutral400),
                          prefixIcon: Icon(
                            Icons.search,
                            color: themeData.neutral400,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () {
                    if (_searchController.text.isEmpty) {
                      context.showSnackbar(message: "Please input link");
                      return;
                    }
                    final isValid =
                        Uri.tryParse(_searchController.text)?.hasAbsolutePath ??
                        false;
                    if (isValid) {
                      context.push(
                        RouterEnums.web3Browser.routeName,
                        extra: _searchController.text,
                      );
                    } else {
                      context.showSnackbar(message: "Link not valid");
                    }
                  },
                  child: const Text(
                    "Search",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Tabs (DApps / History)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Expanded(
                    child: TabBar(
                      controller: _tabController,
                      labelColor: themeData.primaryGreen500,
                      unselectedLabelColor: themeData.neutral500,
                      labelStyle: titleMedium.copyColor(
                        themeData.primaryGreen500,
                      ),
                      unselectedLabelStyle: titleMedium.copyColor(
                        themeData.neutral500,
                      ),
                      indicatorColor: themeData.primaryGreen500,
                      indicatorWeight: 3,
                      tabs: const [Tab(text: 'History')],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // DApps Tab
                  // _buildDAppsTab(),
                  // History Tab
                  _buildHistoryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDAppsTab() {
    return Column(
      children: [
        // Category Filter
        // Container(
        //   height: 50,
        //   padding: const EdgeInsets.symmetric(horizontal: 20),
        //   child: ListView.separated(
        //     scrollDirection: Axis.horizontal,
        //     itemCount: categories.length,
        //     separatorBuilder: (context, index) => const SizedBox(width: 12),
        //     itemBuilder: (context, index) {
        //       final isSelected = _selectedCategoryIndex == index;
        //       return GestureDetector(
        //         onTap: () {
        //           setState(() {
        //             _selectedCategoryIndex = index;
        //           });
        //         },
        //         child: Container(
        //           padding: const EdgeInsets.symmetric(
        //             horizontal: 20,
        //             vertical: 12,
        //           ),
        //           decoration: BoxDecoration(
        //             color:
        //                 isSelected
        //                     ? themeData.primaryGreen500
        //                     : themeData.neutral100,
        //             borderRadius: BorderRadius.circular(25),
        //             border:
        //                 isSelected
        //                     ? null
        //                     : Border.all(color: themeData.neutral200),
        //           ),
        //           child: Row(
        //             mainAxisSize: MainAxisSize.min,
        //             children: [
        //               if (index == 0) // All category with globe icon
        //                 Icon(
        //                   Icons.language,
        //                   size: 16,
        //                   color:
        //                       isSelected ? Colors.white : themeData.neutral500,
        //                 ),
        //               if (index == 0) const SizedBox(width: 6),
        //               Text(
        //                 categories[index],
        //                 style: bodyMedium.copyWith(
        //                   color:
        //                       isSelected ? Colors.white : themeData.neutral500,
        //                   fontWeight:
        //                       isSelected ? FontWeight.w600 : FontWeight.normal,
        //                 ),
        //               ),
        //             ],
        //           ),
        //         ),
        //       );
        //     },
        //   ),
        // ),

        ///   const SizedBox(height: 20),

        // DEX Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: themeData.primaryGreen100,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.swap_horiz,
                  size: 16,
                  color: themeData.primaryGreen500,
                ),
              ),
              const SizedBox(width: 12),
              Text('DEX', style: titleMedium.copyColor(themeData.neutral800)),
            ],
          ),
        ),

        const SizedBox(height: 8),

        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Decentralized exchanges for trading tokens without intermediaries.',
            style: bodySmall.copyColor(themeData.neutral500),
          ),
        ),

        const SizedBox(height: 20),

        // DApps List
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: _getFilteredDApps().length + 1, // +1 for the grid row
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              if (index < _getFilteredDApps().length - 3) {
                // Regular list items
                final dapp = _getFilteredDApps()[index];
                return _buildDAppListItem(dapp);
              } else if (index == _getFilteredDApps().length - 3) {
                // Grid row for last 3 items
                return _buildDAppGridRow();
              }
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: themeData.neutral300),
          const SizedBox(height: 16),
          Text(
            'No history yet',
            style: titleMedium.copyColor(themeData.neutral500),
          ),
          const SizedBox(height: 8),
          Text(
            'Your interaction history will appear here',
            style: bodySmall.copyColor(themeData.neutral400),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDAppListItem(DAppItem dapp) {
    return GestureDetector(
      onTap: () {
        context.push(RouterEnums.web3Browser.routeName, extra: dapp.url);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: themeData.neutral200),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: themeData.neutral100,
              ),
              child: Center(
                child: Text(dapp.icon, style: const TextStyle(fontSize: 24)),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dapp.name,
                    style: titleMedium.copyColor(themeData.neutral800),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    dapp.description,
                    style: bodySmall.copyColor(themeData.neutral500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDAppGridRow() {
    final lastThreeDApps = _getFilteredDApps().take(3).toList();

    return Row(
      children:
          lastThreeDApps.asMap().entries.map((entry) {
            final index = entry.key;
            final dapp = entry.value;

            return Expanded(
              child: GestureDetector(
                onTap: () {
                  context.push(
                    RouterEnums.web3Browser.routeName,
                    extra: dapp.url,
                  );
                },
                child: Container(
                  margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: themeData.neutral200),
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: themeData.neutral100,
                        ),
                        child: Center(
                          child: Text(
                            dapp.icon,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        dapp.name,
                        style: bodyMedium.copyColor(themeData.neutral800),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  List<DAppItem> _getFilteredDApps() {
    if (_selectedCategoryIndex == 0) return dapps; // All
    final selectedCategory = categories[_selectedCategoryIndex];
    return dapps.where((dapp) => dapp.category == selectedCategory).toList();
  }
}

class DAppItem {
  final String name;
  final String description;
  final String icon;
  final String category;
  final String url;

  DAppItem({
    required this.name,
    required this.description,
    required this.icon,
    required this.category,
    required this.url,
  });
}
