import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/report/report_cubit.dart';
import 'package:toii_social/cubit/report/report_state.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/comment/comment_model.dart';
import 'package:toii_social/screen/home/<USER>/widget/bottom_sheet_more_action_comment.dart';
import 'package:toii_social/screen/home/<USER>/widget/like_comment_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class CommentWidget extends StatelessWidget {
  final CommentItemModel commentItemModel;

  const CommentWidget({super.key, required this.commentItemModel});

  void _showCommentOptions(BuildContext context) {
    showMoreActionCommentBottomSheet(
      context: context,
      commentItemModel: commentItemModel,
      onReport: (reasons, customReason) {
        context.read<ReportCubit>().reportComment(
          commentId: commentItemModel.id,
          reasons: reasons,
          customReason: customReason,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeData.neutral200,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar
          AvatarWidget(
            size: 40,
            name:
                commentItemModel.user?.username ??
                commentItemModel.user?.fullName ??
                "",
            imageUrl: commentItemModel.user?.avatar ?? "",
          ),
          const SizedBox(width: 12),
          // Comment Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Username and Time
                Row(
                  children: [
                    Text(
                      (commentItemModel.user?.fullName ?? '').isNotEmpty
                          ? commentItemModel.user?.fullName ?? ""
                          : commentItemModel.user?.username ?? "",
                      style: titleSmall.copyColor(themeData.neutral800),
                    ),
                    if (commentItemModel.user?.isVerified == true) ...[
                      const SizedBox(width: 4),
                      Assets.icons.icVerified.svg(width: 12, height: 12),
                    ],
                    const SizedBox(width: 6),
                    Text(
                      '· ${commentItemModel.updatedAtTime}',
                      style: bodySmall.copyColor(themeData.neutral300),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                // Comment Text
                // showSeeMore
                ReadMoreText(
                  commentItemModel.content,
                  trimLines: 10,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' See more',
                  trimExpandedText: ' Show less',
                  style: bodyMedium.copyColor(themeData.neutral800),
                  moreStyle: labelLarge.copyColor(themeData.primaryGreen500),
                ),

                const SizedBox(height: 8),

                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _actionMore(context),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {},
                      child: Assets.icons.icReply.svg(
                        width: 16,
                        height: 16,
                        colorFilter: ColorFilter.mode(
                          themeData.neutral400,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    const Text(
                      'Reply',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: 12),
          LikeCommentWidget(commentItemModel: commentItemModel),
        ],
      ),
    );
  }

  Widget _actionMore(BuildContext context) {
    return BlocProvider(
      create: (context) => ReportCubit(),
      child: BlocListener<ReportCubit, ReportState>(
        listener: (context, state) {
          if (state.status == ReportStatus.success) {
            context.showSnackbar(
              message: 'Reported successfully',
              duration: const Duration(seconds: 2),
            );
          }
          if (state.status == ReportStatus.failure) {
            context.showSnackbar(
              message: state.errorMessage ?? "",
              duration: const Duration(seconds: 2),
            );
          }
        },
        child: Builder(
          builder: (contextBuilder) {
            return GestureDetector(
              onTap: () => _showCommentOptions(contextBuilder),
              child: Assets.icons.icMore.svg(
                width: 16,
                height: 16,
                colorFilter: ColorFilter.mode(
                  themeData.neutral400,
                  BlendMode.srcIn,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
