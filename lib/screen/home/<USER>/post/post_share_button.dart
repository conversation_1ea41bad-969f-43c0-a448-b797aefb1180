import 'dart:async';

import 'package:flutter/material.dart';
import 'package:toii_social/core/service/share_service.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';

class PostShareButton extends StatefulWidget {
  final PostModel post;

  const PostShareButton({super.key, required this.post});

  @override
  State<PostShareButton> createState() => _PostShareButtonState();
}

class _PostShareButtonState extends State<PostShareButton> {
  Timer? _debounceTimer;
  final bool _isSharing = false;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        ShareService.sharePostUrl(widget.post);
      },
      icon: Assets.icons.icShare1.svg(),
    );
  }
}
