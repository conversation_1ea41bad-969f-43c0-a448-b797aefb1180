import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';
import 'package:toii_social/screen/home/<USER>/post/post_like_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_repost_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_share_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_view_button.dart';

class PostActionBar extends StatelessWidget {
  final PostModel post;
  final bool? isHideRepost;
  const PostActionBar({super.key, required this.post, this.isHideRepost});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 40,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          PostLikeButton(post: post, initialLiked: post.isLiked),

          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                PostIconText(
                  icon: Assets.icons.icComment.svg(),
                  text: post.getViewComment,
                  isBackground: false,
                  onTap: () {
                    context.push(
                      RouterEnums.homeDetails.routeName,
                      extra: HomeDetailsArguments(
                        postModel: post,
                        isForcusComment: true,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 32),
                PostViewButton(post: post),
                const SizedBox(width: 32),
                PostRepostButton(post: post),
                const SizedBox(width: 32),
                PostShareButton(post: post),
              ],
            ),
          ),

          // if (!(isHideRepost == true))
        ],
      ),
    );
  }
}
