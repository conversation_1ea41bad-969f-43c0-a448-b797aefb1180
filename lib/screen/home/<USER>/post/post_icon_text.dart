import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class PostIconText extends StatelessWidget {
  final Widget icon;
  final String text;
  final bool isBackground;
  final VoidCallback? onTap;
  const PostIconText({
    super.key,
    required this.icon,
    required this.text,
    this.isBackground = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        onTap?.call();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          const SizedBox(width: 4),
          if (text.isNotEmpty)
            Container(
              decoration: BoxDecoration(
                color: isBackground ? themeData.neutral100 : null,
                borderRadius: BorderRadius.circular(26),
              ),
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                text,
                style: bodyMedium.copyWith(color: themeData.neutral400),
              ),
            ),
        ],
      ),
    );
  }
}
