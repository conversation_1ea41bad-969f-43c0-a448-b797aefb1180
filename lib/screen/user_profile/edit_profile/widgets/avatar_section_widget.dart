import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';

class AvatarSectionWidget extends StatelessWidget {
  final EditProfileState state;
  final UserModel? user;
  final VoidCallback onAvatarTap;

  const AvatarSectionWidget({
    super.key,
    required this.state,
    required this.user,
    required this.onAvatarTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);
    return Center(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Avatar container with border matching Figma design
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: themeData.black400,
              borderRadius: BorderRadius.circular(32),
              border: Border.all(color: themeData.neutral300, width: 1),
            ),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: themeData.neutral50, width: 1),
                shape: BoxShape.circle,
              ),
              child: CircleAvatar(
                radius: 32,
                backgroundImage: _getAvatarImageProvider(state),
              ),
            ),
          ),
          // Camera icon overlay positioned at bottom right
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: onAvatarTap,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: themeData.white900,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.camera_alt,
                  color: themeData.textPrimary,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ImageProvider _getAvatarImageProvider(EditProfileState state) {
    final avatarPath = state.avatar ?? user?.avatarUrl;

    if (avatarPath == null) {
      // Default avatar
      return const NetworkImage(
        'https://randomuser.me/api/portraits/women/47.jpg',
      );
    }

    // On web, handle differently since File operations are limited
    if (kIsWeb) {
      // For web, if it's a local path, it's likely a blob URL or data URL
      if (avatarPath.startsWith('/') ||
          avatarPath.startsWith('blob:') ||
          avatarPath.startsWith('data:')) {
        return NetworkImage(avatarPath);
      }
      // Otherwise, treat as network URL
      return NetworkImage(avatarPath);
    }

    // For mobile platforms
    // Check if it's a local file path (starts with '/')
    if (avatarPath.startsWith('/')) {
      return FileImage(File(avatarPath));
    }

    // Otherwise, treat as network URL
    return NetworkImage(avatarPath);
  }
}
