import 'package:flutter/material.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class ProfileFormFieldsWidget extends StatelessWidget {
  final EditProfileState state;
  final UserModel? user;
  final TextEditingController usernameController;
  final TextEditingController displayNameController;
  final TextEditingController bioController;
  final Function(String) onUsernameChanged;
  final Function(String) onDisplayNameChanged;
  final Function(String) onBioChanged;

  const ProfileFormFieldsWidget({
    super.key,
    required this.state,
    required this.user,
    required this.usernameController,
    required this.displayNameController,
    required this.bioController,
    required this.onUsernameChanged,
    required this.onDisplayNameChanged,
    required this.onBioChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Username Field (Now Editable)

        // Display Name Field
        _buildLabeledField(
          context: context,
          label: 'Display Name',
          child: TTextField(
            showBorder: true,
            fillColor: Theme.of(context).white900,
            textController: displayNameController,
            hintText: user?.fullName ?? user?.firstName ?? '',
            onChanged: onDisplayNameChanged,
            textStyle: bodyMedium.copyWith(
              color: Theme.of(context).textPrimary,
            ),
            hintStyle: bodyMedium.copyWith(
              color: Theme.of(context).textSecondary,
            ),
          ),
        ),
        const SizedBox(height: 6),
        _buildLabeledField(
          context: context,
          label: 'Username',
          child: TTextField(
            showBorder: true,
            fillColor: Theme.of(context).white900,
            textController: usernameController,
            hintText: 'Enter your username',
            onChanged: onUsernameChanged,
            textStyle: bodyMedium.copyWith(
              color: Theme.of(context).textPrimary,
            ),
            hintStyle: bodyMedium.copyWith(
              color: Theme.of(context).textSecondary,
            ),
          ),
        ),
        const SizedBox(height: 6),
        // Bio Field
        _buildBioField(context),
      ],
    );
  }

  Widget _buildLabeledField({
    required BuildContext context,
    required String label,
    required Widget child,
  }) {
    final themeData = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 5, bottom: 6),
          child: Text(
            label,
            style: titleMedium.copyWith(
              color: themeData.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        child,
      ],
    );
  }

  Widget _buildBioField(BuildContext context) {
    final themeData = Theme.of(context);
    return _buildLabeledField(
      context: context,
      label: 'Bio',
      child: Column(
        children: [
          TTextField(
            showBorder: true,
            fillColor: themeData.white900,
            textController: bioController,
            hintText: 'Write something short about you...',
            maxLines: 4,
            onChanged: onBioChanged,
            textStyle: bodyMedium.copyWith(color: themeData.textPrimary),
            hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8, right: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '${bioController.text.length}/120',
                  style: labelSmall.copyWith(color: themeData.textSecondary),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
