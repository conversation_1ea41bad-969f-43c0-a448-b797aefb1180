import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/follower/follower_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

import 'widget/follower_list.dart';
import 'widget/following_list.dart';

class FollowerFollowingScreen extends StatefulWidget {
  const FollowerFollowingScreen({super.key, this.user, this.initialTab = 0});

  final UserModel? user;
  final int initialTab;

  @override
  State<FollowerFollowingScreen> createState() =>
      _FollowerFollowingScreenState();
}

class _FollowerFollowingScreenState extends State<FollowerFollowingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late FollowerCubit _followerCubit;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTab,
    );
    _followerCubit = GetIt.instance<FollowerCubit>();

    // Load initial data based on the selected tab
    if (widget.initialTab == 0) {
      _followerCubit.getFollowers(
        userId: widget.user?.id,
        isMyAccount:
            widget.user?.id ==
            GetIt.instance<ProfileCubit>().state.userModel?.id,
      );
    } else {
      _followerCubit.getFollowing(
        userId: widget.user?.id,
        isMyAccount:
            widget.user?.id ==
            GetIt.instance<ProfileCubit>().state.userModel?.id,
      );
    }

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        if (_tabController.index == 0) {
          _followerCubit.getFollowers(
            userId: widget.user?.id,
            isMyAccount:
                widget.user?.id ==
                GetIt.instance<ProfileCubit>().state.userModel?.id,
          );
        } else {
          _followerCubit.getFollowing(
            userId: widget.user?.id,
            isMyAccount:
                widget.user?.id ==
                GetIt.instance<ProfileCubit>().state.userModel?.id,
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);

    return BlocProvider.value(
      value: _followerCubit,
      child: Scaffold(
        backgroundColor: themeData.neutral50,
        appBar: BaseAppBar(
          title:
              (widget.user?.fullName ?? '').isNotEmpty
                  ? widget.user?.fullName
                  : widget.user?.username ?? 'User',
          centerTitle: false,
          actions: [
            IconButton(
              icon: Icon(Icons.person_add, color: themeData.neutral400),
              onPressed: () {
                // TODO: Implement add friend functionality
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab Bar Section
            Container(
              color: themeData.neutral50,
              child: Column(
                children: [
                  // Tab Navigation
                  Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    height: 36,
                    child: AnimatedBuilder(
                      animation: _tabController,
                      builder: (context, child) {
                        final tabs = ['Followers', 'Following'];
                        return ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () {
                                _tabController.animateTo(index);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                ),
                                child: IntrinsicWidth(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        tabs[index],
                                        style: titleLarge.copyColor(
                                          _tabController.index == index
                                              ? themeData.neutral800
                                              : themeData.neutral400,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 4,
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color:
                                                      _tabController.index ==
                                                              index
                                                          ? themeData
                                                              .primaryGreen500
                                                          : Colors.transparent,
                                                ),
                                              ),
                                            ),
                                            Expanded(child: const SizedBox()),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox();
                          },
                          itemCount: tabs.length,
                        );
                      },
                    ),
                  ),
                  // Follower Count
                  AnimatedBuilder(
                    animation: _tabController,
                    builder: (context, child) {
                      return BlocBuilder<FollowerCubit, FollowerState>(
                        builder: (context, state) {
                          final count =
                              _tabController.index == 0
                                  ? state.totalFollowers
                                  : state.totalFollowing;
                          final label =
                              _tabController.index == 0
                                  ? 'Followers'
                                  : 'Following';

                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            alignment: Alignment.centerLeft,
                            child: Text(
                              '$count $label',
                              style: titleMedium.copyWith(
                                color: themeData.neutral400,
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  FollowerList(
                    onFollowStatusChanged: () {
                      _followerCubit.getFollowers(
                        userId: widget.user?.id,
                        isMyAccount:
                            widget.user?.id ==
                            GetIt.instance<ProfileCubit>().state.userModel?.id,
                      );
                    },
                    isMyAccount:
                        widget.user?.id ==
                        GetIt.instance<ProfileCubit>().state.userModel?.id,
                  ),
                  FollowingList(
                    onFollowStatusChanged: () {
                      _followerCubit.getFollowing(
                        userId: widget.user?.id,
                        isMyAccount:
                            widget.user?.id ==
                            GetIt.instance<ProfileCubit>().state.userModel?.id,
                      );
                    },
                    isMyAccount:
                        widget.user?.id ==
                        GetIt.instance<ProfileCubit>().state.userModel?.id,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
