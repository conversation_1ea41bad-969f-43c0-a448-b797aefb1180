import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/follower/follower_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

import 'user_list_item.dart';

class FollowingList extends StatelessWidget {
  const FollowingList({
    super.key,
    this.isMyAccount = true,
    this.onFollowStatusChanged,
  });

  final bool isMyAccount;
  final VoidCallback? onFollowStatusChanged;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FollowerCubit, FollowerState>(
      builder: (context, state) {
        if (state.status.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.status.isFailure) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Failed to load following',
                  style: titleMedium.copyWith(
                    color: context.themeData.neutral400,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (onFollowStatusChanged != null) {
                      onFollowStatusChanged!();
                    } else {
                      context.read<FollowerCubit>().refreshFollowing();
                    }
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state.following.isEmpty) {
          return Center(
            child: Text(
              'Not following anyone yet',
              style: titleMedium.copyWith(color: context.themeData.neutral400),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: state.following.length,
          itemBuilder: (context, index) {
            final user = state.following[index];
            return UserListItem(
              user: user,
              isFollowing: true,
              isMyAccount: isMyAccount,
              showFollowButton:
                  isMyAccount, // Only show follow button when viewing own account
              onFollowStatusChanged: onFollowStatusChanged,
            );
          },
        );
      },
    );
  }
}
