import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/logout/logout_cubit.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class LogoutDialog {
  /// Show logout confirmation dialog
  static void show(BuildContext context, LogoutCubit logoutCubit) {
    final themeData = context.themeData;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return BlocProvider.value(
          value: logoutCubit,
          child: BlocConsumer<LogoutCubit, LogoutState>(
            listener: (context, state) {
              if (state.status.isSuccess) {
                Navigator.of(dialogContext).pop();
                GetIt.instance.unregister<ProfileCubit>();
                GetIt.instance.registerLazySingleton(() => ProfileCubit());
                // Navigate to initial route which will redirect to start screen
                context.go(RouterEnums.inital.routeName);
                context.showSnackbar(message: "Logged out successfully");
              } else if (state.status.isFailure) {
                Navigator.of(dialogContext).pop();
                context.showSnackbar(
                  message: state.errorMessage ?? "Logout failed",
                );
              }
            },
            builder: (context, state) {
              return AlertDialog(
                backgroundColor: themeData.neutral100,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'Log out',
                  style: titleLarge.copyWith(
                    color: themeData.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                content: Text(
                  'Are you sure you want to log out?',
                  style: bodyMedium.copyWith(color: themeData.textSecondary),
                ),
                actions: [
                  TextButton(
                    onPressed:
                        state.status.isLoading
                            ? null
                            : () {
                              Navigator.of(dialogContext).pop();
                            },
                    child: Text(
                      'Cancel',
                      style: labelLarge.copyWith(
                        color: themeData.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed:
                        state.status.isLoading
                            ? null
                            : () {
                              logoutCubit.logout();
                            },
                    child:
                        state.status.isLoading
                            ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  themeData.red500,
                                ),
                              ),
                            )
                            : Text(
                              'Log out',
                              style: labelLarge.copyWith(
                                color: themeData.red500,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }
}
