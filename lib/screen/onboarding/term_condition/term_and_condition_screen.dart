import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/core/constant/constant.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/utils/url_luncher/url_luncher.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart';
 
class TermAndConditionScreen extends StatefulWidget {
  const TermAndConditionScreen({super.key});

  @override
  State<TermAndConditionScreen> createState() => _TermAndConditionScreenState();
}

class _TermAndConditionScreenState extends State<TermAndConditionScreen> {
  late final WebViewController _controller;
 
  bool _isAgree = false;
  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      
    } else {
      _controller =
          WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setNavigationDelegate(
              NavigationDelegate(
                onProgress: (int progress) {
                  // Update loading bar.
                },
                onPageStarted: (String url) {},
                onPageFinished: (String url) {},
                onHttpError: (HttpResponseError error) {},
                onWebResourceError: (WebResourceError error) {},
              ),
            )
            ..loadRequest(Uri.parse(Constant.baseUrlPrivacy));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //  appBar: AppBar(backgroundColor: Colors.transparent),
      extendBodyBehindAppBar: true,
      backgroundColor: themeData.neutral50,
      body: SafeArea(
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: SvgPicture.asset(Assets.icons.icGao.path),
            ),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 104),
                  Assets.images.imgGaoTerm.image(),
                  Expanded(
                    child:
                         
                            WebViewWidget(controller: _controller),
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isAgree = !_isAgree;
                      });
                      // _registerValidateCubit.setAgreed();
                      // _registerValidateCubit.checkEnableButton(
                      //   _registerType == RegisterType.phone,
                      //   email: _emailController.text,
                      //   phoneNumber: _phoneController.text,
                      // );
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          _isAgree
                              ? Assets.icons.icChecked.path
                              : Assets.icons.icCheckbox.path,
                        ),

                        const SizedBox(width: 8),
                        Expanded(
                          child: EasyRichText(
                            S.current.register_terms_agreement,
                            defaultStyle: bodyMedium.copyColor(
                              themeData.neutral400,
                            ),
                            patternList: [
                              EasyRichTextPattern(
                                targetString:
                                    S.current.register_terms_of_service,
                                style: bodyMedium.copyColor(
                                  themeData.primaryGreen500,
                                ),
                                recognizer:
                                    TapGestureRecognizer()
                                      ..onTap = () {
                                        launchUrlCustom(
                                          Constant.baseUrlPrivacy,
                                        );
                                      },
                              ),
                              EasyRichTextPattern(
                                targetString: S.current.register_privacy_policy,
                                style: bodyMedium.copyColor(
                                  themeData.primaryGreen500,
                                ),
                                recognizer:
                                    TapGestureRecognizer()
                                      ..onTap = () {
                                        launchUrlCustom(
                                          Constant.baseUrlPrivacy,
                                        );
                                      },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  TSButton.primary(
                    isEnabled: _isAgree,
                    title: "Continue",
                    onPressed: () {
                      Navigator.of(context).pop(true);
                    },
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop(false);
              },
              child: Container(
                padding: const EdgeInsets.only(left: 16, top: kIsWeb ? 32 : 0),
                child: SvgPicture.asset(Assets.icons.icBack.path),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
